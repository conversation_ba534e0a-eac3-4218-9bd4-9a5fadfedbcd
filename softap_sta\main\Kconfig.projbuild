menu "Example Configuration"

    menu "SoftAP Configuration"
        comment "SoftAP Configuration"

        config ESP_WIFI_AP_SSID
            string "WiFi AP SSID"
            default "myssid"
            help
                SSID (network name) of the AP for the example to connect to.

        config ESP_WIFI_AP_PASSWORD
            string "WiFi AP Password"
            default "mypassword"
            help
                WiFi password of the AP for the example to use.

        config ESP_WIFI_AP_CHANNEL
            int "WiFi AP Channel"
            range 1 14
            default 1
            help
                WiFi channel (network channel) of the AP for the example to use.

        config ESP_MAX_STA_CONN_AP
            int "Maximal STA connections"
            default 4
            help
                Max number of the STA connects to AP.
    endmenu

    menu "STA Configuration"
        comment "STA Configuration"

        config ESP_WIFI_REMOTE_AP_SSID
            string "WiFi Remote AP SSID"
            default "otherapssid"
            help
                SSID (network name) for the example's sta to connect to.

        config ESP_WIFI_REMOTE_AP_PASSWORD
            string "WiFi Remote AP Password"
            default "otherappassword"
            help
                WiFi password for the example to use.

        config ESP_MAXIMUM_STA_RETRY
            int "Maximum retry"
            default 5
            help
                Set the maximum retry value to prevent the station from continuously
                attempting to reconnect to the Access Point (AP) when the AP doesn't exist.

        choice ESP_WIFI_SCAN_AUTH_MODE_THRESHOLD
            prompt "WiFi Scan auth mode threshold"
            default ESP_WIFI_AUTH_WPA2_PSK
            help
                The weakest authmode to accept in the scan mode.
                This value defaults to ESP_WIFI_AUTH_WPA2_PSK in case password is present
                and ESP_WIFI_AUTH_OPEN is used. Please select ESP_WIFI_AUTH_WEP / ESP_WIFI_AUTH_WPA_PSK
                in case AP is operating in WEP / WPA mode.

            config ESP_WIFI_AUTH_OPEN
                bool "OPEN"
            config ESP_WIFI_AUTH_WEP
                bool "WEP"
            config ESP_WIFI_AUTH_WPA_PSK
                bool "WPA PSK"
            config ESP_WIFI_AUTH_WPA2_PSK
                bool "WPA2 PSK"
            config ESP_WIFI_AUTH_WPA_WPA2_PSK
                bool "WPA/WPA2 PSK"
            config ESP_WIFI_AUTH_WPA3_PSK
                bool "WPA3 PSK"
            config ESP_WIFI_AUTH_WPA2_WPA3_PSK
                bool "WPA2/WPA3 PSK"
            config ESP_WIFI_AUTH_WAPI_PSK
                bool "WAPI PSK"
        endchoice

    endmenu
endmenu
