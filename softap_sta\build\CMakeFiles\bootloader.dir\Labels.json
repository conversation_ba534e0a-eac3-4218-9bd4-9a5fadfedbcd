{"sources": [{"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/CMakeFiles/bootloader"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/CMakeFiles/bootloader.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/CMakeFiles/bootloader-complete.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}